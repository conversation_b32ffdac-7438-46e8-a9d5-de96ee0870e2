// Gmail API REST implementation for email sending
import { GMAIL_CONFIG, validateGmailConfig } from '../config/gmail';

// Gmail API REST service class
class GmailAPIService {
  private static async getAccessToken(): Promise<string> {
    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: GMAIL_CONFIG.CLIENT_ID,
          client_secret: GMAIL_CONFIG.CLIENT_SECRET,
          refresh_token: GMAIL_CONFIG.REFRESH_TOKEN,
          grant_type: 'refresh_token',
        }),
      });

      const data = await response.json();
      return data.access_token;
    } catch (error) {
      console.error('Error getting access token:', error);
      throw error;
    }
  }

  static async sendEmail(to: string, subject: string, htmlBody: string): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();

      // Create email message in RFC 2822 format
      const emailMessage = [
        `To: ${to}`,
        `From: ${GMAIL_CONFIG.FROM_EMAIL}`,
        `Subject: ${subject}`,
        'Content-Type: text/html; charset=utf-8',
        '',
        htmlBody
      ].join('\n');

      // Encode message in base64url format
      const encodedMessage = btoa(emailMessage)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

      // Send email via Gmail API
      const response = await fetch('https://gmail.googleapis.com/gmail/v1/users/me/messages/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          raw: encodedMessage,
        }),
      });

      if (!response.ok) {
        throw new Error(`Gmail API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Email sent successfully via Gmail API:', result.id);
      return true;
    } catch (error) {
      console.error('Error sending email via Gmail API:', error);
      return false;
    }
  }
}

// HTML Email Templates
class EmailTemplates {
  static createBookingEmailHTML(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>New Booking Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2c5530; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .booking-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; color: #666; }
          .highlight { color: #2c5530; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🦁 New Booking Confirmation</h1>
            <p>Warriors of Africa Safari</p>
          </div>
          <div class="content">
            <h2>Booking Details</h2>
            <div class="booking-details">
              <p><strong>Booking ID:</strong> <span class="highlight">${data.booking_id}</span></p>
              <p><strong>Tour:</strong> ${data.tour_title}</p>
              <p><strong>Customer:</strong> ${data.customer_name}</p>
              <p><strong>Email:</strong> ${data.customer_email}</p>
              <p><strong>Phone:</strong> ${data.customer_phone}</p>
              <p><strong>Start Date:</strong> ${data.start_date}</p>
              <p><strong>Group Size:</strong> ${data.group_size} adults</p>
              <p><strong>Children:</strong> ${data.children_count}</p>
              <p><strong>Accommodation:</strong> ${data.accommodation}</p>
              <p><strong>Total Price:</strong> <span class="highlight">${data.total_price}</span></p>
              <p><strong>Add-ons:</strong> ${data.add_ons}</p>
              <p><strong>Special Requests:</strong> ${data.special_requests}</p>
            </div>

            <h3>Traveler Details</h3>
            <div class="booking-details">
              <pre>${data.travelers_details}</pre>
            </div>

            <p><strong>Booking Date:</strong> ${data.booking_date} at ${data.booking_time}</p>
          </div>
          <div class="footer">
            <p>Warriors of Africa Safari<br>
            Email: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  static createCustomTourEmailHTML(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>New Custom Tour Request</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2c5530; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .tour-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; color: #666; }
          .highlight { color: #2c5530; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🌍 New Custom Tour Request</h1>
            <p>Warriors of Africa Safari</p>
          </div>
          <div class="content">
            <h2>Custom Tour Request Details</h2>
            <div class="tour-details">
              <p><strong>Request ID:</strong> <span class="highlight">${data.request_id}</span></p>
              <p><strong>Customer:</strong> ${data.customer_name}</p>
              <p><strong>Email:</strong> ${data.customer_email}</p>
              <p><strong>Phone:</strong> ${data.customer_phone}</p>
              <p><strong>Duration:</strong> ${data.duration}</p>
              <p><strong>Participants:</strong> ${data.participants}</p>
              <p><strong>Budget Range:</strong> <span class="highlight">${data.budget_range}</span></p>
              <p><strong>Start Date:</strong> ${data.start_date}</p>
              <p><strong>Destinations:</strong> ${data.destinations}</p>
              <p><strong>Interests:</strong> ${data.interests}</p>
              <p><strong>Accommodation:</strong> ${data.accommodation}</p>
              <p><strong>Activities:</strong> ${data.activities}</p>
              <p><strong>Fitness Level:</strong> ${data.fitness_level}</p>
              <p><strong>Photography Interest:</strong> ${data.photography_interest}</p>
              <p><strong>Special Requests:</strong> ${data.special_requests}</p>
            </div>

            <p><strong>Request Date:</strong> ${data.request_date} at ${data.request_time}</p>
          </div>
          <div class="footer">
            <p>Warriors of Africa Safari<br>
            Email: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  static createContactEmailHTML(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>New Contact Form Submission</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2c5530; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .contact-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; color: #666; }
          .highlight { color: #2c5530; font-weight: bold; }
          .message { background: #f0f8f0; padding: 15px; border-left: 4px solid #2c5530; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📧 New Contact Form Submission</h1>
            <p>Warriors of Africa Safari</p>
          </div>
          <div class="content">
            <h2>Contact Details</h2>
            <div class="contact-details">
              <p><strong>Name:</strong> ${data.customer_name}</p>
              <p><strong>Email:</strong> ${data.customer_email}</p>
              <p><strong>Phone:</strong> ${data.customer_phone}</p>
              <p><strong>Subject:</strong> <span class="highlight">${data.inquiry_subject}</span></p>
              <p><strong>Category:</strong> ${data.inquiry_category}</p>
            </div>

            <h3>Message</h3>
            <div class="message">
              <p>${data.message}</p>
            </div>

            <p><strong>Contact Date:</strong> ${data.contact_date} at ${data.contact_time}</p>
          </div>
          <div class="footer">
            <p>Warriors of Africa Safari<br>
            Email: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

export interface BookingEmailData {
  tourTitle: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  startDate: string;
  groupSize: number;
  childrenCount: number;
  accommodation: string;
  totalPrice: number;
  specialRequests?: string;
  bookingId: string;
  travelers: Array<{
    name: string;
    age: number;
    nationality: string;
    dietaryRequirements?: string;
  }>;
  addOns: string[];
}

export interface CustomTourEmailData {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  duration: number;
  participants: number;
  budget: number[];
  startDate: string;
  destinations: string[];
  interests: string[];
  accommodation: string;
  activities: string[];
  specialRequests: string;
  fitnessLevel: string;
  photographyInterest: boolean;
  requestId: string;
}

export class EmailService {
  /**
   * Validate Gmail configuration before sending emails
   */
  private static validateConfig(): boolean {
    return validateGmailConfig();
  }

  /**
   * Send booking confirmation email to admin
   */
  static async sendBookingNotification(bookingData: BookingEmailData): Promise<boolean> {
    if (!this.validateConfig()) {
      console.error('Gmail API configuration is invalid. Please check your credentials.');
      return false;
    }
    try {
      const templateData = {
        booking_id: bookingData.bookingId,
        tour_title: bookingData.tourTitle,
        customer_name: bookingData.customerName,
        customer_email: bookingData.customerEmail,
        customer_phone: bookingData.customerPhone || 'Not provided',
        start_date: bookingData.startDate,
        group_size: bookingData.groupSize,
        children_count: bookingData.childrenCount,
        accommodation: bookingData.accommodation,
        total_price: `$${bookingData.totalPrice ? bookingData.totalPrice.toLocaleString() : '0'}`,
        special_requests: bookingData.specialRequests || 'None',
        travelers_details: this.formatTravelersDetails(bookingData.travelers),
        add_ons: bookingData.addOns.length > 0 ? bookingData.addOns.join(', ') : 'None',
        booking_date: new Date().toLocaleDateString(),
        booking_time: new Date().toLocaleTimeString(),
      };

      const subject = `New Booking Confirmation - ${bookingData.tourTitle}`;
      const htmlBody = EmailTemplates.createBookingEmailHTML(templateData);

      const success = await GmailAPIService.sendEmail(
        GMAIL_CONFIG.TO_EMAIL,
        subject,
        htmlBody
      );

      if (success) {
        console.log('Booking email sent successfully via Gmail API');
        return true;
      } else {
        throw new Error('Failed to send email via Gmail API');
      }
    } catch (error) {
      console.error('Error sending booking email:', error);
      return false;
    }
  }

  /**
   * Send custom tour request email to admin
   */
  static async sendCustomTourNotification(tourData: CustomTourEmailData): Promise<boolean> {
    if (!this.validateConfig()) {
      console.error('Gmail API configuration is invalid. Please check your credentials.');
      return false;
    }

    try {
      const templateData = {
        request_id: tourData.requestId,
        customer_name: tourData.customerName,
        customer_email: tourData.customerEmail,
        customer_phone: tourData.customerPhone,
        duration: `${tourData.duration} days`,
        participants: tourData.participants,
        budget_range: `$${tourData.budget[0].toLocaleString()} - $${tourData.budget[1]?.toLocaleString() || tourData.budget[0].toLocaleString()}`,
        start_date: tourData.startDate,
        destinations: tourData.destinations.join(', '),
        interests: tourData.interests.join(', '),
        accommodation: tourData.accommodation,
        activities: tourData.activities.join(', '),
        special_requests: tourData.specialRequests || 'None',
        fitness_level: tourData.fitnessLevel,
        photography_interest: tourData.photographyInterest ? 'Yes' : 'No',
        request_date: new Date().toLocaleDateString(),
        request_time: new Date().toLocaleTimeString(),
      };

      const subject = `New Custom Tour Request - ${tourData.customerName}`;
      const htmlBody = EmailTemplates.createCustomTourEmailHTML(templateData);

      const success = await GmailAPIService.sendEmail(
        GMAIL_CONFIG.TO_EMAIL,
        subject,
        htmlBody
      );

      if (success) {
        console.log('Custom tour email sent successfully via Gmail API');
        return true;
      } else {
        throw new Error('Failed to send email via Gmail API');
      }
    } catch (error) {
      console.error('Error sending custom tour email:', error);
      return false;
    }
  }

  /**
   * Format travelers details for email
   */
  private static formatTravelersDetails(travelers: BookingEmailData['travelers']): string {
    if (!travelers || travelers.length === 0) {
      return 'No traveler details provided';
    }

    return travelers.map((traveler, index) => {
      return `Traveler ${index + 1}:
- Name: ${traveler.name}
- Age: ${traveler.age}
- Nationality: ${traveler.nationality}
- Dietary Requirements: ${traveler.dietaryRequirements || 'None'}`;
    }).join('\n\n');
  }

  /**
   * Send general contact email (for contact form submissions)
   */
  static async sendContactNotification(contactData: {
    name: string;
    email: string;
    phone?: string;
    subject: string;
    message: string;
    category: string;
  }): Promise<boolean> {
    if (!this.validateConfig()) {
      console.error('Gmail API configuration is invalid. Please check your credentials.');
      return false;
    }

    try {
      const templateData = {
        customer_name: contactData.name,
        customer_email: contactData.email,
        customer_phone: contactData.phone || 'Not provided',
        inquiry_subject: contactData.subject,
        inquiry_category: contactData.category,
        message: contactData.message,
        contact_date: new Date().toLocaleDateString(),
        contact_time: new Date().toLocaleTimeString(),
      };

      const subject = `New Contact Form Submission - ${contactData.subject}`;
      const htmlBody = EmailTemplates.createContactEmailHTML(templateData);

      const success = await GmailAPIService.sendEmail(
        GMAIL_CONFIG.TO_EMAIL,
        subject,
        htmlBody
      );

      if (success) {
        console.log('Contact email sent successfully via Gmail API');
        return true;
      } else {
        throw new Error('Failed to send email via Gmail API');
      }
    } catch (error) {
      console.error('Error sending contact email:', error);
      return false;
    }
  }
}
