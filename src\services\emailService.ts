// EmailJS implementation for real email sending
import emailjs from '@emailjs/browser';

// EmailJS configuration - Using your actual EmailJS credentials
const EMAILJS_SERVICE_ID = 'service_fglqy8j';
const EMAILJS_TEMPLATE_ID_BOOKING = 'template_booking_forward';
const EMAILJS_TEMPLATE_ID_CUSTOM_TOUR = 'template_custom_tour_forward';
const EMAILJS_TEMPLATE_ID_CONTACT = 'template_contact_forward';
const EMAILJS_PUBLIC_KEY = 'tGnQ7cVsiXlu_yNuG';

// Initialize EmailJS with your public key
emailjs.init(EMAILJS_PUBLIC_KEY);

export interface BookingEmailData {
  tourTitle: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  startDate: string;
  groupSize: number;
  childrenCount: number;
  accommodation: string;
  totalPrice: number;
  specialRequests?: string;
  bookingId: string;
  travelers: Array<{
    name: string;
    age: number;
    nationality: string;
    dietaryRequirements?: string;
  }>;
  addOns: string[];
}

export interface CustomTourEmailData {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  duration: number;
  participants: number;
  budget: number[];
  startDate: string;
  destinations: string[];
  interests: string[];
  accommodation: string;
  activities: string[];
  specialRequests: string;
  fitnessLevel: string;
  photographyInterest: boolean;
  requestId: string;
}

export class EmailService {
  /**
   * Send booking confirmation email to admin
   */
  static async sendBookingNotification(bookingData: BookingEmailData): Promise<boolean> {
    try {
      const templateParams = {
        to_email: '<EMAIL>',
        subject: `New Booking Confirmation - ${bookingData.tourTitle}`,
        booking_id: bookingData.bookingId,
        tour_title: bookingData.tourTitle,
        customer_name: bookingData.customerName,
        customer_email: bookingData.customerEmail,
        customer_phone: bookingData.customerPhone || 'Not provided',
        start_date: bookingData.startDate,
        group_size: bookingData.groupSize,
        children_count: bookingData.childrenCount,
        accommodation: bookingData.accommodation,
        total_price: `$${bookingData.totalPrice.toLocaleString()}`,
        special_requests: bookingData.specialRequests || 'None',
        travelers_details: this.formatTravelersDetails(bookingData.travelers),
        add_ons: bookingData.addOns.length > 0 ? bookingData.addOns.join(', ') : 'None',
        booking_date: new Date().toLocaleDateString(),
        booking_time: new Date().toLocaleTimeString(),
      };

      const response = await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID_BOOKING,
        templateParams
      );

      console.log('Booking email sent successfully:', response);
      return true;
    } catch (error) {
      console.error('Error sending booking email:', error);
      return false;
    }
  }

  /**
   * Send custom tour request email to admin
   */
  static async sendCustomTourNotification(tourData: CustomTourEmailData): Promise<boolean> {
    try {
      const templateParams = {
        to_email: '<EMAIL>',
        subject: `New Custom Tour Request - ${tourData.customerName}`,
        request_id: tourData.requestId,
        customer_name: tourData.customerName,
        customer_email: tourData.customerEmail,
        customer_phone: tourData.customerPhone,
        duration: `${tourData.duration} days`,
        participants: tourData.participants,
        budget_range: `$${tourData.budget[0].toLocaleString()} - $${tourData.budget[1]?.toLocaleString() || tourData.budget[0].toLocaleString()}`,
        start_date: tourData.startDate,
        destinations: tourData.destinations.join(', '),
        interests: tourData.interests.join(', '),
        accommodation: tourData.accommodation,
        activities: tourData.activities.join(', '),
        special_requests: tourData.specialRequests || 'None',
        fitness_level: tourData.fitnessLevel,
        photography_interest: tourData.photographyInterest ? 'Yes' : 'No',
        request_date: new Date().toLocaleDateString(),
        request_time: new Date().toLocaleTimeString(),
      };

      const response = await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID_CUSTOM_TOUR,
        templateParams
      );

      console.log('Custom tour email sent successfully:', response);
      return true;
    } catch (error) {
      console.error('Error sending custom tour email:', error);
      return false;
    }
  }

  /**
   * Format travelers details for email
   */
  private static formatTravelersDetails(travelers: BookingEmailData['travelers']): string {
    if (!travelers || travelers.length === 0) {
      return 'No traveler details provided';
    }

    return travelers.map((traveler, index) => {
      return `Traveler ${index + 1}:
- Name: ${traveler.name}
- Age: ${traveler.age}
- Nationality: ${traveler.nationality}
- Dietary Requirements: ${traveler.dietaryRequirements || 'None'}`;
    }).join('\n\n');
  }

  /**
   * Send general contact email (for contact form submissions)
   */
  static async sendContactNotification(contactData: {
    name: string;
    email: string;
    phone?: string;
    subject: string;
    message: string;
    category: string;
  }): Promise<boolean> {
    try {
      const templateParams = {
        to_email: '<EMAIL>',
        subject: `New Contact Form Submission - ${contactData.subject}`,
        customer_name: contactData.name,
        customer_email: contactData.email,
        customer_phone: contactData.phone || 'Not provided',
        inquiry_subject: contactData.subject,
        inquiry_category: contactData.category,
        message: contactData.message,
        contact_date: new Date().toLocaleDateString(),
        contact_time: new Date().toLocaleTimeString(),
      };

      const response = await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID_CONTACT,
        templateParams
      );

      console.log('Contact email sent successfully:', response);
      return true;
    } catch (error) {
      console.error('Error sending contact email:', error);
      return false;
    }
  }
}
